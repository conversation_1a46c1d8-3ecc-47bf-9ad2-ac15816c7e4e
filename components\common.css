/* 微信小程序通用样式 */
:root {
    --wx-primary: #07C160;
    --wx-primary-dark: #06AD56;
    --wx-warning: #FA9D3B;
    --wx-danger: #FA5151;
    --wx-info: #10AEFF;
    --wx-text-primary: #000000;
    --wx-text-secondary: #353535;
    --wx-text-tertiary: #888888;
    --wx-bg-primary: #FFFFFF;
    --wx-bg-secondary: #F7F7F7;
    --wx-border: #E5E5E5;
    --express-blue: #1890FF;
    --express-orange: #FF6B35;
}

.wx-container {
    width: 375px;
    height: 667px;
    background: var(--wx-bg-secondary);
    position: relative;
    overflow: hidden;
}

.wx-status-bar {
    height: 44px;
    background: var(--wx-bg-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
}

.wx-nav-bar {
    height: 44px;
    background: var(--wx-bg-primary);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--wx-border);
    position: relative;
}

.wx-nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 600;
    color: var(--wx-text-primary);
}

.wx-content {
    height: calc(667px - 88px);
    overflow-y: auto;
    background: var(--wx-bg-secondary);
}

.wx-button {
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    padding: 12px 24px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wx-button-primary {
    background: var(--wx-primary);
    color: white;
}

.wx-button-primary:hover {
    background: var(--wx-primary-dark);
}

.wx-card {
    background: var(--wx-bg-primary);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.wx-divider {
    height: 8px;
    background: var(--wx-bg-secondary);
}