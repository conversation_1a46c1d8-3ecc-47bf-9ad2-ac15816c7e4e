/* Apple Design Language - 苹果设计语言样式系统 */
:root {
    /* Apple Color Palette - 苹果色彩系统 */
    --apple-blue: #007AFF;
    --apple-blue-dark: #0051D5;
    --apple-blue-light: #5AC8FA;
    --apple-green: #34C759;
    --apple-green-dark: #30D158;
    --apple-orange: #FF9500;
    --apple-red: #FF3B30;
    --apple-purple: #AF52DE;
    --apple-pink: #FF2D92;
    --apple-yellow: #FFCC00;
    --apple-indigo: #5856D6;
    --apple-teal: #5AC8FA;

    /* Apple Gray Scale - 苹果灰度系统 */
    --apple-gray-1: #8E8E93;
    --apple-gray-2: #AEAEB2;
    --apple-gray-3: #C7C7CC;
    --apple-gray-4: #D1D1D6;
    --apple-gray-5: #E5E5EA;
    --apple-gray-6: #F2F2F7;

    /* Apple Text Colors - 苹果文字颜色 */
    --apple-text-primary: #000000;
    --apple-text-secondary: #3C3C43;
    --apple-text-tertiary: #3C3C4399;
    --apple-text-quaternary: #3C3C432E;
    --apple-text-placeholder: #3C3C434D;

    /* Apple Background Colors - 苹果背景颜色 */
    --apple-bg-primary: #FFFFFF;
    --apple-bg-secondary: #F2F2F7;
    --apple-bg-tertiary: #FFFFFF;
    --apple-bg-grouped-primary: #F2F2F7;
    --apple-bg-grouped-secondary: #FFFFFF;
    --apple-bg-grouped-tertiary: #F2F2F7;

    /* Apple System Colors - 苹果系统颜色 */
    --apple-separator: #3C3C4349;
    --apple-separator-opaque: #C6C6C8;
    --apple-link: #007AFF;
    --apple-fill-primary: #78788033;
    --apple-fill-secondary: #78788028;
    --apple-fill-tertiary: #7676801E;
    --apple-fill-quaternary: #74748014;

    /* Apple Shadows - 苹果阴影系统 */
    --apple-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --apple-shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --apple-shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    --apple-shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    --apple-shadow-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

    /* Apple Border Radius - 苹果圆角系统 */
    --apple-radius-small: 6px;
    --apple-radius-medium: 10px;
    --apple-radius-large: 16px;
    --apple-radius-xlarge: 20px;
    --apple-radius-continuous: 22px;

    /* Apple Spacing - 苹果间距系统 */
    --apple-spacing-xs: 4px;
    --apple-spacing-sm: 8px;
    --apple-spacing-md: 16px;
    --apple-spacing-lg: 24px;
    --apple-spacing-xl: 32px;
    --apple-spacing-xxl: 48px;

    /* Apple Typography - 苹果字体系统 */
    --apple-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --apple-font-weight-regular: 400;
    --apple-font-weight-medium: 500;
    --apple-font-weight-semibold: 600;
    --apple-font-weight-bold: 700;

    /* Apple Animation - 苹果动画系统 */
    --apple-ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
    --apple-ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
    --apple-ease-in: cubic-bezier(0.4, 0.0, 1, 1);
    --apple-duration-fast: 0.15s;
    --apple-duration-normal: 0.25s;
    --apple-duration-slow: 0.35s;
}

.wx-container {
    width: 375px;
    height: 667px;
    background: var(--wx-bg-secondary);
    position: relative;
    overflow: hidden;
}

.wx-status-bar {
    height: 44px;
    background: var(--wx-bg-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
}

.wx-nav-bar {
    height: 44px;
    background: var(--wx-bg-primary);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--wx-border);
    position: relative;
}

.wx-nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 600;
    color: var(--wx-text-primary);
}

.wx-content {
    height: calc(667px - 88px);
    overflow-y: auto;
    background: var(--wx-bg-secondary);
}

.wx-button {
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    padding: 12px 24px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wx-button-primary {
    background: var(--wx-primary);
    color: white;
}

.wx-button-primary:hover {
    background: var(--wx-primary-dark);
}

.wx-card {
    background: var(--wx-bg-primary);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.wx-divider {
    height: 8px;
    background: var(--wx-bg-secondary);
}