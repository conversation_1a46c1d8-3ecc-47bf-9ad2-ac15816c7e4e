/* Apple Design Language - 苹果设计语言样式系统 */
:root {
    /* Apple Color Palette - 苹果色彩系统 */
    --apple-blue: #007AFF;
    --apple-blue-dark: #0051D5;
    --apple-blue-light: #5AC8FA;
    --apple-green: #34C759;
    --apple-green-dark: #30D158;
    --apple-orange: #FF9500;
    --apple-red: #FF3B30;
    --apple-purple: #AF52DE;
    --apple-pink: #FF2D92;
    --apple-yellow: #FFCC00;
    --apple-indigo: #5856D6;
    --apple-teal: #5AC8FA;

    /* Apple Gray Scale - 苹果灰度系统 */
    --apple-gray-1: #8E8E93;
    --apple-gray-2: #AEAEB2;
    --apple-gray-3: #C7C7CC;
    --apple-gray-4: #D1D1D6;
    --apple-gray-5: #E5E5EA;
    --apple-gray-6: #F2F2F7;

    /* Apple Text Colors - 苹果文字颜色 */
    --apple-text-primary: #000000;
    --apple-text-secondary: #3C3C43;
    --apple-text-tertiary: #3C3C4399;
    --apple-text-quaternary: #3C3C432E;
    --apple-text-placeholder: #3C3C434D;

    /* Apple Background Colors - 苹果背景颜色 */
    --apple-bg-primary: #FFFFFF;
    --apple-bg-secondary: #F2F2F7;
    --apple-bg-tertiary: #FFFFFF;
    --apple-bg-grouped-primary: #F2F2F7;
    --apple-bg-grouped-secondary: #FFFFFF;
    --apple-bg-grouped-tertiary: #F2F2F7;

    /* Apple System Colors - 苹果系统颜色 */
    --apple-separator: #3C3C4349;
    --apple-separator-opaque: #C6C6C8;
    --apple-link: #007AFF;
    --apple-fill-primary: #78788033;
    --apple-fill-secondary: #78788028;
    --apple-fill-tertiary: #7676801E;
    --apple-fill-quaternary: #74748014;

    /* Apple Shadows - 苹果阴影系统 */
    --apple-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --apple-shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --apple-shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    --apple-shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    --apple-shadow-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

    /* Apple Border Radius - 苹果圆角系统 */
    --apple-radius-small: 6px;
    --apple-radius-medium: 10px;
    --apple-radius-large: 16px;
    --apple-radius-xlarge: 20px;
    --apple-radius-continuous: 22px;

    /* Apple Spacing - 苹果间距系统 */
    --apple-spacing-xs: 4px;
    --apple-spacing-sm: 8px;
    --apple-spacing-md: 16px;
    --apple-spacing-lg: 24px;
    --apple-spacing-xl: 32px;
    --apple-spacing-xxl: 48px;

    /* Apple Typography - 苹果字体系统 */
    --apple-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --apple-font-weight-regular: 400;
    --apple-font-weight-medium: 500;
    --apple-font-weight-semibold: 600;
    --apple-font-weight-bold: 700;

    /* Apple Animation - 苹果动画系统 */
    --apple-ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
    --apple-ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
    --apple-ease-in: cubic-bezier(0.4, 0.0, 1, 1);
    --apple-duration-fast: 0.15s;
    --apple-duration-normal: 0.25s;
    --apple-duration-slow: 0.35s;
}

/* Apple Container - 苹果容器样式 */
.apple-container {
    width: 375px;
    height: 667px;
    background: var(--apple-bg-grouped-primary);
    position: relative;
    overflow: hidden;
    font-family: var(--apple-font-family);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Apple Status Bar - 苹果状态栏 */
.apple-status-bar {
    height: 44px;
    background: var(--apple-bg-primary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--apple-spacing-md);
    font-size: 14px;
    font-weight: var(--apple-font-weight-semibold);
    color: var(--apple-text-primary);
    border-bottom: 0.5px solid var(--apple-separator);
}

/* Apple Navigation Bar - 苹果导航栏 */
.apple-nav-bar {
    height: 44px;
    background: var(--apple-bg-primary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    padding: 0 var(--apple-spacing-md);
    border-bottom: 0.5px solid var(--apple-separator);
    position: relative;
}

.apple-nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: var(--apple-font-weight-semibold);
    color: var(--apple-text-primary);
    letter-spacing: -0.41px;
}

.apple-nav-button {
    color: var(--apple-blue);
    font-size: 17px;
    font-weight: var(--apple-font-weight-regular);
    background: none;
    border: none;
    cursor: pointer;
    transition: opacity var(--apple-duration-fast) var(--apple-ease-out);
}

.apple-nav-button:hover {
    opacity: 0.6;
}

/* Apple Content Area - 苹果内容区域 */
.apple-content {
    height: calc(667px - 88px);
    overflow-y: auto;
    background: var(--apple-bg-grouped-primary);
    -webkit-overflow-scrolling: touch;
}

/* Apple Button System - 苹果按钮系统 */
.apple-button {
    border-radius: var(--apple-radius-medium);
    font-size: 17px;
    font-weight: var(--apple-font-weight-semibold);
    padding: 12px var(--apple-spacing-lg);
    border: none;
    cursor: pointer;
    transition: all var(--apple-duration-normal) var(--apple-ease-out);
    font-family: var(--apple-font-family);
    letter-spacing: -0.41px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.apple-button-primary {
    background: var(--apple-blue);
    color: white;
    box-shadow: var(--apple-shadow-1);
}

.apple-button-primary:hover {
    background: var(--apple-blue-dark);
    transform: translateY(-1px);
    box-shadow: var(--apple-shadow-2);
}

.apple-button-primary:active {
    transform: translateY(0);
    box-shadow: var(--apple-shadow-1);
}

.apple-button-secondary {
    background: var(--apple-fill-secondary);
    color: var(--apple-blue);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.apple-button-secondary:hover {
    background: var(--apple-fill-primary);
}

.apple-button-tertiary {
    background: transparent;
    color: var(--apple-blue);
}

.apple-button-tertiary:hover {
    background: var(--apple-fill-tertiary);
}

/* Apple Card System - 苹果卡片系统 */
.apple-card {
    background: var(--apple-bg-grouped-secondary);
    border-radius: var(--apple-radius-large);
    padding: var(--apple-spacing-md);
    margin: var(--apple-spacing-sm) var(--apple-spacing-md);
    box-shadow: var(--apple-shadow-1);
    border: 0.5px solid var(--apple-separator);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.apple-card-elevated {
    background: var(--apple-bg-elevated);
    box-shadow: var(--apple-shadow-2);
}

.apple-card-inset {
    background: var(--apple-bg-grouped-secondary);
    border-radius: var(--apple-radius-medium);
    margin: 0;
    border: none;
    box-shadow: none;
}

/* Apple Divider - 苹果分割线 */
.apple-divider {
    height: var(--apple-spacing-sm);
    background: var(--apple-bg-grouped-primary);
}

.apple-separator {
    height: 0.5px;
    background: var(--apple-separator);
    margin: 0 var(--apple-spacing-md);
}

/* Apple Glass Effect - 苹果毛玻璃效果 */
.apple-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 0.5px solid rgba(255, 255, 255, 0.2);
}

.apple-glass-dark {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 0.5px solid rgba(255, 255, 255, 0.1);
}

/* Apple Typography - 苹果字体样式 */
.apple-title-large {
    font-size: 34px;
    font-weight: var(--apple-font-weight-bold);
    line-height: 1.2;
    letter-spacing: 0.37px;
    color: var(--apple-text-primary);
}

.apple-title {
    font-size: 28px;
    font-weight: var(--apple-font-weight-bold);
    line-height: 1.2;
    letter-spacing: 0.36px;
    color: var(--apple-text-primary);
}

.apple-title-2 {
    font-size: 22px;
    font-weight: var(--apple-font-weight-bold);
    line-height: 1.3;
    letter-spacing: 0.35px;
    color: var(--apple-text-primary);
}

.apple-title-3 {
    font-size: 20px;
    font-weight: var(--apple-font-weight-semibold);
    line-height: 1.3;
    letter-spacing: 0.38px;
    color: var(--apple-text-primary);
}

.apple-headline {
    font-size: 17px;
    font-weight: var(--apple-font-weight-semibold);
    line-height: 1.3;
    letter-spacing: -0.41px;
    color: var(--apple-text-primary);
}

.apple-body {
    font-size: 17px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.4;
    letter-spacing: -0.41px;
    color: var(--apple-text-primary);
}

.apple-callout {
    font-size: 16px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.3;
    letter-spacing: -0.32px;
    color: var(--apple-text-primary);
}

.apple-subhead {
    font-size: 15px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.3;
    letter-spacing: -0.24px;
    color: var(--apple-text-primary);
}

.apple-footnote {
    font-size: 13px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.4;
    letter-spacing: -0.08px;
    color: var(--apple-text-secondary);
}

.apple-caption {
    font-size: 12px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.3;
    letter-spacing: 0px;
    color: var(--apple-text-secondary);
}

.apple-caption-2 {
    font-size: 11px;
    font-weight: var(--apple-font-weight-regular);
    line-height: 1.2;
    letter-spacing: 0.07px;
    color: var(--apple-text-tertiary);
}

/* Apple List Styles - 苹果列表样式 */
.apple-list {
    background: var(--apple-bg-grouped-secondary);
    border-radius: var(--apple-radius-large);
    margin: var(--apple-spacing-md);
    overflow: hidden;
}

.apple-list-item {
    background: var(--apple-bg-grouped-secondary);
    padding: var(--apple-spacing-md);
    border-bottom: 0.5px solid var(--apple-separator);
    display: flex;
    align-items: center;
    min-height: 44px;
    transition: background-color var(--apple-duration-fast) var(--apple-ease-out);
}

.apple-list-item:last-child {
    border-bottom: none;
}

.apple-list-item:hover {
    background: var(--apple-fill-tertiary);
}

.apple-list-item:active {
    background: var(--apple-fill-secondary);
}

.apple-list-item-content {
    flex: 1;
    display: flex;
    align-items: center;
}

.apple-list-item-icon {
    width: 29px;
    height: 29px;
    border-radius: var(--apple-radius-small);
    margin-right: var(--apple-spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.apple-list-item-text {
    flex: 1;
}

.apple-list-item-title {
    font-size: 17px;
    font-weight: var(--apple-font-weight-regular);
    color: var(--apple-text-primary);
    line-height: 1.3;
    letter-spacing: -0.41px;
}

.apple-list-item-subtitle {
    font-size: 15px;
    font-weight: var(--apple-font-weight-regular);
    color: var(--apple-text-secondary);
    line-height: 1.3;
    letter-spacing: -0.24px;
    margin-top: 2px;
}

.apple-list-item-detail {
    font-size: 17px;
    font-weight: var(--apple-font-weight-regular);
    color: var(--apple-text-tertiary);
    letter-spacing: -0.41px;
}

.apple-list-item-chevron {
    color: var(--apple-gray-3);
    font-size: 14px;
    margin-left: var(--apple-spacing-sm);
}