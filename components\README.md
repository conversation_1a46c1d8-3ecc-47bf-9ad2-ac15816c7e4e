# 组件库文档

## 设计规范

### 颜色系统
- 主色调：微信绿 #07C160
- 辅助色：快递蓝 #1890FF、快递橙 #FF6B35
- 文字色：主文字 #000000、次要文字 #353535、辅助文字 #888888
- 背景色：主背景 #FFFFFF、次要背景 #F7F7F7
- 边框色：#E5E5E5

### 字体规范
- 字体栈：-apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif
- 标题：17px/22px，字重 600
- 正文：16px/24px，字重 400
- 辅助文字：14px/20px，字重 400
- 小字：12px/16px，字重 400

### 间距系统
- 基础单位：8px
- 页面边距：16px
- 卡片内边距：16px
- 元素间距：8px、16px、24px、32px

### 圆角规范
- 卡片圆角：8px
- 按钮圆角：4px
- 头像圆角：50%
- 标签圆角：12px

### 阴影规范
- 卡片阴影：0 1px 3px rgba(0,0,0,0.1)
- 浮层阴影：0 4px 12px rgba(0,0,0,0.15)

## 交互规范

### 点击反馈
- 按钮点击：背景色加深 10%
- 卡片点击：添加 2px 边框高亮
- 列表项点击：背景色变为 #F5F5F5

### 动画规范
- 过渡时长：0.3s
- 缓动函数：ease-in-out
- 页面切换：slide 动画
- 加载动画：旋转动画

### 状态设计
- 加载状态：骨架屏 + 加载指示器
- 空状态：插图 + 提示文字 + 操作按钮
- 错误状态：错误图标 + 错误信息 + 重试按钮