# Apple Design Language 组件系统

这个目录包含了严格遵循苹果设计语言的样式组件系统。

## 文件说明

- `common.css` - 苹果设计语言样式系统，包含完整的设计令牌和组件样式

## 核心特性

### 🎨 完整的设计令牌系统
- 苹果标准色彩系统
- 精确的字体层级
- 标准化间距系统
- 多层级阴影系统
- 苹果标准圆角规范

### ✨ 苹果风格组件
- **容器系统**：`apple-container`, `apple-content`
- **导航系统**：`apple-nav-bar`, `apple-status-bar`
- **按钮系统**：`apple-button-primary`, `apple-button-secondary`, `apple-button-tertiary`
- **卡片系统**：`apple-card`, `apple-card-elevated`, `apple-card-inset`
- **列表系统**：`apple-list`, `apple-list-item`
- **字体系统**：`apple-title`, `apple-headline`, `apple-body`, `apple-footnote`

### 🌟 视觉效果
- **毛玻璃效果**：`apple-glass`, `apple-glass-dark`
- **微交互动画**：悬停、点击状态
- **优雅阴影**：5个层级的深度表达
- **流畅过渡**：苹果标准缓动曲线

## 使用方法

### 基础引入
```html
<link rel="stylesheet" href="components/common.css">
```

### 基础容器结构
```html
<div class="apple-container">
    <div class="apple-status-bar">
        <span>9:41</span>
        <span>📶 📶 📶 🔋</span>
    </div>

    <div class="apple-nav-bar">
        <button class="apple-nav-button">‹</button>
        <div class="apple-nav-title">页面标题</div>
        <button class="apple-nav-button">⋯</button>
    </div>

    <div class="apple-content">
        <!-- 页面内容 -->
    </div>
</div>
```

### 卡片组件
```html
<div class="apple-card">
    <h2 class="apple-headline">卡片标题</h2>
    <p class="apple-body">卡片内容</p>
    <button class="apple-button-primary">操作按钮</button>
</div>
```

### 列表组件
```html
<div class="apple-list">
    <div class="apple-list-item">
        <div class="apple-list-item-content">
            <div class="apple-list-item-icon">🎯</div>
            <div class="apple-list-item-text">
                <div class="apple-list-item-title">列表标题</div>
                <div class="apple-list-item-subtitle">列表副标题</div>
            </div>
        </div>
        <div class="apple-list-item-detail">详情</div>
        <div class="apple-list-item-chevron">›</div>
    </div>
</div>
```

## 设计原则

### 1. 简约克制
- 充分留白，减少视觉噪音
- 精简的界面元素
- 清晰的信息层次

### 2. 一致性
- 统一的设计语言
- 标准化的组件规范
- 可预测的交互模式

### 3. 优雅细致
- 精致的微交互
- 自然的动画过渡
- 细腻的视觉效果

## CSS变量系统

所有设计令牌都通过CSS变量定义，支持主题定制：

```css
:root {
    /* 颜色系统 */
    --apple-blue: #007AFF;
    --apple-green: #34C759;
    --apple-red: #FF3B30;

    /* 间距系统 */
    --apple-spacing-xs: 4px;
    --apple-spacing-sm: 8px;
    --apple-spacing-md: 16px;

    /* 字体系统 */
    --apple-font-family: -apple-system, BlinkMacSystemFont, ...;
    --apple-font-weight-regular: 400;
    --apple-font-weight-semibold: 600;

    /* 动画系统 */
    --apple-duration-fast: 0.15s;
    --apple-ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
}
```

## 最佳实践

1. **保持简洁**：避免过度设计和不必要的装饰
2. **遵循规范**：使用标准的间距、颜色和字体
3. **注重细节**：关注微交互和过渡效果
4. **一致体验**：在整个应用中保持设计一致性
5. **性能优先**：合理使用动画和视觉效果

---

*本组件系统完全遵循苹果人机界面指南，为用户提供熟悉、直观的苹果式体验。*