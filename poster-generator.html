<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成分享海报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="components/common.css">
</head>
<body>
    <div class="apple-container">
        <!-- 状态栏 -->
        <div class="apple-status-bar">
            <span>9:41</span>
            <span>📶 📶 📶 🔋</span>
        </div>

        <!-- 导航栏 -->
        <div class="apple-nav-bar">
            <button class="apple-nav-button">‹</button>
            <div class="apple-nav-title">生成分享海报</div>
            <button class="apple-nav-button">⋯</button>
        </div>

        <!-- 内容区域 -->
        <div class="apple-content">
            <!-- 海报模板选择 -->
            <div class="apple-card">
                <h2 class="apple-headline" style="margin-bottom: var(--apple-spacing-md);">选择海报模板</h2>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--apple-spacing-md);">
                    <div style="border: 2px solid var(--apple-blue); border-radius: var(--apple-radius-medium); overflow: hidden; background: var(--apple-bg-primary);">
                        <div style="background: linear-gradient(135deg, var(--apple-blue) 0%, var(--apple-purple) 100%); height: 128px; display: flex; align-items: center; justify-content: center; color: white; position: relative;">
                            <div class="apple-glass" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1;"></div>
                            <div style="text-align: center; position: relative; z-index: 1;">
                                <div class="apple-headline" style="color: white; margin-bottom: var(--apple-spacing-xs);">新人专享</div>
                                <div class="apple-footnote" style="color: rgba(255, 255, 255, 0.9);">邀请好友送大礼</div>
                            </div>
                        </div>
                        <div style="padding: var(--apple-spacing-sm); text-align: center;">
                            <span class="apple-caption" style="color: var(--apple-blue); font-weight: var(--apple-font-weight-medium);">已选择</span>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gradient-to-br from-orange-500 to-red-500 h-32 flex items-center justify-center text-white">
                            <div class="text-center">
                                <div class="text-lg font-bold">限时活动</div>
                                <div class="text-sm">冲榜赢大奖</div>
                            </div>
                        </div>
                        <div class="p-2 text-center">
                            <span class="text-xs text-gray-500">点击选择</span>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gradient-to-br from-green-500 to-teal-500 h-32 flex items-center justify-center text-white">
                            <div class="text-center">
                                <div class="text-lg font-bold">会员推广</div>
                                <div class="text-sm">长期收益</div>
                            </div>
                        </div>
                        <div class="p-2 text-center">
                            <span class="text-xs text-gray-500">点击选择</span>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gradient-to-br from-pink-500 to-rose-500 h-32 flex items-center justify-center text-white">
                            <div class="text-center">
                                <div class="text-lg font-bold">团队奖励</div>
                                <div class="text-sm">建设团队</div>
                            </div>
                        </div>
                        <div class="p-2 text-center">
                            <span class="text-xs text-gray-500">点击选择</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="wx-divider"></div>
            
            <!-- 海报预览 -->
            <div class="bg-white p-4">
                <h2 class="text-lg font-semibold mb-4">海报预览</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                    <!-- 海报内容 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden mx-auto" style="width: 280px;">
                        <!-- 海报头部 -->
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 p-6 text-white text-center">
                            <div class="mb-4">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto flex items-center justify-center mb-3">
                                    <span class="text-2xl">📦</span>
                                </div>
                                <h3 class="text-xl font-bold">快递小助手</h3>
                                <p class="text-sm opacity-90">让寄快递更简单</p>
                            </div>
                        </div>
                        
                        <!-- 活动信息 -->
                        <div class="p-6">
                            <div class="text-center mb-4">
                                <h4 class="text-lg font-bold text-gray-800 mb-2">新人专享大礼包</h4>
                                <p class="text-sm text-gray-600">邀请好友注册即可获得</p>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-2 mb-4">
                                <div class="text-center bg-red-50 rounded-lg p-3">
                                    <div class="text-lg font-bold text-red-500">¥10</div>
                                    <div class="text-xs text-gray-500">现金</div>
                                </div>
                                <div class="text-center bg-orange-50 rounded-lg p-3">
                                    <div class="text-lg font-bold text-orange-500">7天</div>
                                    <div class="text-xs text-gray-500">会员</div>
                                </div>
                                <div class="text-center bg-blue-50 rounded-lg p-3">
                                    <div class="text-lg font-bold text-blue-500">5张</div>
                                    <div class="text-xs text-gray-500">优惠券</div>
                                </div>
                            </div>
                            
                            <!-- 用户信息 -->
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3">
                                        张
                                    </div>
                                    <div>
                                        <div class="font-medium">张三邀请您</div>
                                        <div class="text-xs text-gray-500">已帮助1000+用户省钱</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 二维码 -->
                            <div class="text-center">
                                <div class="w-24 h-24 bg-gray-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <span class="text-xs text-gray-500">二维码</span>
                                </div>
                                <p class="text-xs text-gray-500">长按识别小程序码</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="wx-divider"></div>
            
            <!-- 个性化设置 -->
            <div class="bg-white p-4">
                <h2 class="text-lg font-semibold mb-4">个性化设置</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">邀请文案</label>
                        <textarea class="w-full p-3 border border-gray-200 rounded-lg text-sm" rows="3" placeholder="自定义邀请文案...">我在用快递小助手，寄快递超方便！新人注册送大礼包，快来试试吧~</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">显示个人信息</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span class="text-sm">显示头像</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="mr-2">
                                <span class="text-sm">显示昵称</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2">
                                <span class="text-sm">显示邀请数</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作按钮 -->
            <div class="bg-white p-4 border-t border-gray-100">
                <div class="grid grid-cols-2 gap-3">
                    <button class="bg-gray-100 text-gray-700 py-3 rounded-lg font-medium">预览海报</button>
                    <button class="bg-blue-500 text-white py-3 rounded-lg font-medium">生成并分享</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>