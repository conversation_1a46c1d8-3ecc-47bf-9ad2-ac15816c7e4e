<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>寄快递小程序 - 分享功能原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 3rem;
            padding: 3rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        .phone-frame {
            width: 375px;
            height: 667px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 30px;
            overflow: hidden;
            position: relative;
            background: #000;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .phone-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        .page-title {
            text-align: center;
            margin-bottom: 1.5rem;
            font-weight: 600;
            color: #1d1d1f;
            font-size: 17px;
            letter-spacing: -0.41px;
        }
        .main-title {
            text-align: center;
            margin-bottom: 3rem;
            font-weight: 700;
            color: #1d1d1f;
            font-size: 48px;
            letter-spacing: -0.003em;
            line-height: 1.1;
        }
        .main-subtitle {
            text-align: center;
            margin-bottom: 4rem;
            font-weight: 400;
            color: #86868b;
            font-size: 21px;
            letter-spacing: 0.011em;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container mx-auto py-8">
        <h1 class="main-title">寄快递小程序</h1>
        <p class="main-subtitle">分享功能原型设计 - Apple Design Language</p>
        
        <div class="prototype-container">
            <!-- 分享菜单主页 -->
            <div>
                <div class="page-title">分享菜单主页</div>
                <div class="phone-frame">
                    <iframe src="share-menu.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
            </div>
            
            <!-- 活动列表页 -->
            <div>
                <div class="page-title">活动列表页</div>
                <div class="phone-frame">
                    <iframe src="activity-list.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
            </div>
            
            <!-- 海报生成页 -->
            <div>
                <div class="page-title">海报生成页</div>
                <div class="phone-frame">
                    <iframe src="poster-generator.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
            </div>
            
            <!-- 分享统计页 -->
            <div>
                <div class="page-title">分享统计页</div>
                <div class="phone-frame">
                    <iframe src="share-stats.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>