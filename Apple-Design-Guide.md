# 苹果设计语言实现指南

## 设计原则

本项目严格遵循苹果设计语言（Apple Design Language），实现了以下核心设计原则：

### 1. 简约克制的界面布局
- **充分留白**：使用苹果标准间距系统（4px、8px、16px、24px、32px、48px）
- **减少视觉噪音**：移除不必要的装饰元素，专注于内容本身
- **清晰的层次结构**：通过字体大小、颜色和间距建立信息层次

### 2. 半透明磨砂效果
- **毛玻璃效果**：使用 `backdrop-filter: blur(20px)` 实现苹果标志性的毛玻璃效果
- **适度应用**：在导航栏、卡片和浮层中谨慎使用，避免过度
- **透明度控制**：使用 rgba 颜色值精确控制透明度

### 3. 精致的微交互与过渡动效
- **缓动函数**：使用苹果标准缓动曲线
  - `cubic-bezier(0.4, 0.0, 0.2, 1)` - 标准缓动
  - `cubic-bezier(0.0, 0.0, 0.2, 1)` - 缓出
  - `cubic-bezier(0.4, 0.0, 1, 1)` - 缓入
- **动画时长**：0.15s（快速）、0.25s（标准）、0.35s（慢速）
- **悬停效果**：按钮和交互元素的细微反馈

### 4. 优雅的阴影与深度表达
- **分层阴影系统**：5个层级的阴影深度
- **自然光影**：模拟真实世界的光照效果
- **适度使用**：避免过度阴影造成视觉混乱

## 色彩系统

### 主色调
- **蓝色**：`#007AFF` - 主要交互色
- **绿色**：`#34C759` - 成功状态
- **橙色**：`#FF9500` - 警告状态
- **红色**：`#FF3B30` - 错误状态
- **紫色**：`#AF52DE` - 特殊强调

### 灰度系统
- **文字颜色**：
  - 主要文字：`#000000`
  - 次要文字：`#3C3C43`
  - 三级文字：`rgba(60, 60, 67, 0.6)`
  - 四级文字：`rgba(60, 60, 67, 0.18)`

- **背景颜色**：
  - 主背景：`#FFFFFF`
  - 次背景：`#F2F2F7`
  - 分组背景：`#F2F2F7`

## 字体系统

### 字体族
```css
font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

### 字体层级
- **大标题**：34px / Bold / 1.2行高
- **标题**：28px / Bold / 1.2行高
- **二级标题**：22px / Bold / 1.3行高
- **三级标题**：20px / Semibold / 1.3行高
- **标题文字**：17px / Semibold / 1.3行高
- **正文**：17px / Regular / 1.4行高
- **说明文字**：15px / Regular / 1.3行高
- **脚注**：13px / Regular / 1.4行高
- **标注**：12px / Regular / 1.3行高

## 组件系统

### 按钮
- **主要按钮**：蓝色背景，白色文字，圆角10px
- **次要按钮**：半透明背景，蓝色文字
- **三级按钮**：透明背景，蓝色文字
- **最小高度**：44px（符合苹果触控标准）

### 卡片
- **标准卡片**：白色背景，16px圆角，轻微阴影
- **提升卡片**：增强阴影效果
- **内嵌卡片**：无阴影，适用于列表内部

### 列表
- **分组列表**：圆角容器，分割线
- **列表项**：44px最小高度，左对齐图标和文字
- **交互反馈**：悬停和点击状态

## 实现细节

### CSS变量系统
所有设计令牌都通过CSS变量定义，便于维护和主题切换：

```css
:root {
  --apple-blue: #007AFF;
  --apple-spacing-md: 16px;
  --apple-radius-medium: 10px;
  /* ... 更多变量 */
}
```

### 响应式设计
- 使用相对单位和弹性布局
- 支持不同屏幕尺寸
- 保持设计一致性

### 性能优化
- 使用CSS变量减少重复代码
- 合理使用GPU加速属性
- 优化动画性能

## 使用指南

### 类名规范
- 使用 `apple-` 前缀区分苹果设计组件
- 语义化命名，如 `apple-button-primary`
- 保持命名一致性

### 最佳实践
1. **保持简洁**：避免过度设计
2. **一致性**：在整个应用中保持设计一致
3. **可访问性**：确保足够的对比度和触控目标大小
4. **性能**：合理使用动画和效果

## 文件结构

```
components/
├── common.css          # 苹果设计语言样式系统
├── README.md          # 组件说明文档
share-menu.html        # 分享菜单页面（已更新）
activity-list.html     # 活动列表页面（已更新）
poster-generator.html  # 海报生成页面（已更新）
share-stats.html       # 分享统计页面
index.html            # 主页面（已更新）
```

## 更新日志

### v2.0 - Apple Design Language
- 完全重构CSS样式系统
- 实现苹果设计语言规范
- 添加毛玻璃效果和微交互
- 优化色彩和字体系统
- 提升整体视觉体验

---

*本设计系统严格遵循苹果人机界面指南（Human Interface Guidelines），为用户提供熟悉、直观的交互体验。*
